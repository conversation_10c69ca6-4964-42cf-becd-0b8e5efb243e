using AlpacaMomentumBot.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using System.Text;
using Xunit;

namespace AlpacaMomentumBot.Tests.Services;

public class MarketDataServiceTests
{
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<ILogger<MarketDataService>> _mockLogger;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly MarketDataService _marketDataService;

    public MarketDataServiceTests()
    {
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockLogger = new Mock<ILogger<MarketDataService>>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockHttpClient = new Mock<HttpClient>();

        // Set up rate limit helpers
        var mockAlpacaRateLimitHelper = new Mock<IAlpacaRateLimitHelper>();
        var mockPolygonRateLimitHelper = new Mock<IPolygonRateLimitHelper>();

        // Set up rate limit helpers to execute actions directly (no rate limiting in tests)
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IPage<IBar>>>>(), It.IsAny<string>()))
            .Returns<Func<Task<IPage<IBar>>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IAccount>>>(), It.IsAny<string>()))
            .Returns<Func<Task<IAccount>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IReadOnlyList<IPosition>>>>(), It.IsAny<string>()))
            .Returns<Func<Task<IReadOnlyList<IPosition>>>, string>((func, key) => func());
        mockAlpacaRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IReadOnlyList<IOrder>>>>(), It.IsAny<string>()))
            .Returns<Func<Task<IReadOnlyList<IOrder>>>, string>((func, key) => func());

        mockPolygonRateLimitHelper.Setup(x => x.ExecuteAsync(It.IsAny<Func<Task<IPage<IBar>>>>(), It.IsAny<string>()))
            .Returns<Func<Task<IPage<IBar>>>, string>((func, key) => func());
        mockPolygonRateLimitHelper.Setup(x => x.ExecuteAsync<decimal?>(It.IsAny<Func<Task<decimal?>>>(), It.IsAny<string>()))
            .Returns<Func<Task<decimal?>>, string>((func, key) => func());

        _mockAlpacaFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        _mockAlpacaFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockAlpacaFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockAlpacaRateLimitHelper.Object);

        _mockPolygonFactory.Setup(x => x.CreateClient()).Returns(_mockHttpClient.Object);
        _mockPolygonFactory.Setup(x => x.GetRateLimitHelper()).Returns(mockPolygonRateLimitHelper.Object);

        _marketDataService = new MarketDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetStockBarsAsync_WithValidSymbol_ReturnsAlpacaBars()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;
        
        var mockBars = CreateMockBars(10);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        _mockDataClient.Verify(x => x.ListHistoricalBarsAsync(
            It.IsAny<HistoricalBarsRequest>(), default), Times.Once);
    }

    [Fact]
    public async Task GetStockBarsAsync_WithMultipleSymbols_ReturnsAllValidBars()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;

        var mockBars = CreateMockBars(5);
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockBarsAsync(symbols, startDate, endDate);

        // Assert
        result.Should().HaveCount(3);
        result.Keys.Should().Contain(symbols);
        foreach (var kvp in result)
        {
            kvp.Value.Items.Should().HaveCount(5);
        }
    }

    [Fact]
    public async Task GetStockMinuteBarsAsync_WithValidSymbol_ReturnsMinuteBars()
    {
        // Arrange
        var symbol = "AAPL";
        var startDate = DateTime.UtcNow.AddHours(-6);
        var endDate = DateTime.UtcNow;

        var mockBars = CreateMockBars(360); // 6 hours of minute bars
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(mockBars);

        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(
            It.Is<HistoricalBarsRequest>(r => r.TimeFrame == BarTimeFrame.Minute), default))
            .ReturnsAsync(mockResponse.Object);

        // Act
        var result = await _marketDataService.GetStockMinuteBarsAsync(symbol, startDate, endDate);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(360);
    }

    [Fact]
    public async Task GetAccountAsync_ReturnsAccountInformation()
    {
        // Arrange
        var mockAccount = new Mock<IAccount>();
        mockAccount.Setup(x => x.Equity).Returns(100000m);
        mockAccount.Setup(x => x.BuyingPower).Returns(200000m);

        _mockTradingClient.Setup(x => x.GetAccountAsync(default))
            .ReturnsAsync(mockAccount.Object);

        // Act
        var result = await _marketDataService.GetAccountAsync();

        // Assert
        result.Should().NotBeNull();
        result.Equity.Should().Be(100000m);
        result.BuyingPower.Should().Be(200000m);
    }

    [Fact]
    public async Task GetPositionsAsync_ReturnsCurrentPositions()
    {
        // Arrange
        var mockPositions = new List<IPosition>
        {
            CreateMockPosition("AAPL", 100, 150m),
            CreateMockPosition("MSFT", 50, 300m)
        };

        // Set up the mock to return the positions for any call to ListPositionsAsync
        _mockTradingClient.Setup(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockPositions);

        // Act
        var result = await _marketDataService.GetPositionsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(p => p.Symbol == "AAPL");
        result.Should().Contain(p => p.Symbol == "MSFT");

        // Verify the mock was called
        _mockTradingClient.Verify(x => x.ListPositionsAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetRecentFillsAsync_ReturnsRecentOrders()
    {
        // Arrange
        var mockOrders = new List<IOrder>
        {
            CreateMockOrder("AAPL", OrderSide.Buy, 100, 150m),
            CreateMockOrder("MSFT", OrderSide.Sell, 50, 300m)
        };

        _mockTradingClient.Setup(x => x.ListOrdersAsync(
            It.Is<ListOrdersRequest>(r => r.OrderStatusFilter == OrderStatusFilter.All), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mockOrders);

        // Act
        var result = await _marketDataService.GetRecentFillsAsync(10);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(2);
        result.Should().Contain(o => o.Symbol == "AAPL");
        result.Should().Contain(o => o.Symbol == "MSFT");

        // Verify the mock was called
        _mockTradingClient.Verify(x => x.ListOrdersAsync(
            It.Is<ListOrdersRequest>(r => r.OrderStatusFilter == OrderStatusFilter.All), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetStockBarsAsync_WhenAlpacaThrows_ThrowsException()
    {
        // Arrange
        var symbol = "INVALID";
        var startDate = DateTime.UtcNow.AddDays(-30);
        var endDate = DateTime.UtcNow;
        
        _mockDataClient.Setup(x => x.ListHistoricalBarsAsync(It.IsAny<HistoricalBarsRequest>(), default))
            .ThrowsAsync(new InvalidOperationException("API Error"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _marketDataService.GetStockBarsAsync(symbol, startDate, endDate));
    }

    [Fact]
    public void GetIndexValueAsync_TestPlaceholder_PassesForNow()
    {
        // Note: This is a placeholder test for the GetIndexValueAsync method
        // In a real implementation, you'd need to properly mock HttpClient using HttpClientFactory
        // and test the actual HTTP calls and JSON parsing

        // For now, we'll just verify the service can be instantiated
        var service = new MarketDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            _mockLogger.Object);

        service.Should().NotBeNull();
    }

    [Fact]
    public void PolygonTimestampConversion_ShouldConvertCorrectly()
    {
        // Arrange - Test timestamp conversion from Polygon milliseconds to UTC DateTime
        var polygonTimestampMs = 1640995200000L; // January 1, 2022 00:00:00 UTC
        var expectedUtcDateTime = new DateTime(2022, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // Act - Simulate the conversion that happens in TryParsePolygonBar/TryParseIndexBar
        var convertedDateTime = DateTimeOffset.FromUnixTimeMilliseconds(polygonTimestampMs).UtcDateTime;

        // Assert
        convertedDateTime.Should().Be(expectedUtcDateTime);
        convertedDateTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [Fact]
    public void PolygonTimestampConversion_ShouldHandleMarketHours()
    {
        // Arrange - Test market hours timestamp (9:30 AM EST = 2:30 PM UTC on trading day)
        // Let's use a known timestamp: 1640959800000L = December 31, 2021 14:10:00 UTC
        var marketOpenTimestampMs = 1640959800000L;
        var expectedUtcDateTime = new DateTime(2021, 12, 31, 14, 10, 0, DateTimeKind.Utc);

        // Act
        var convertedDateTime = DateTimeOffset.FromUnixTimeMilliseconds(marketOpenTimestampMs).UtcDateTime;

        // Assert
        convertedDateTime.Should().Be(expectedUtcDateTime);
        convertedDateTime.Kind.Should().Be(DateTimeKind.Utc);
    }

    [Fact]
    public void PolygonBar_ShouldMaintainTimezoneConsistencyWithAlpacaBars()
    {
        // Arrange - Create mock Alpaca bar and Polygon bar for same timestamp
        var testTimestamp = new DateTime(2022, 1, 1, 15, 30, 0, DateTimeKind.Utc);

        var mockAlpacaBar = new Mock<IBar>();
        mockAlpacaBar.Setup(x => x.TimeUtc).Returns(testTimestamp);
        mockAlpacaBar.Setup(x => x.Close).Returns(100m);

        var polygonBar = new PolygonBar("AAPL", testTimestamp, 99m, 101m, 98m, 100m, 1000000);
        var polygonBarWrapper = new PolygonBarWrapper(polygonBar);

        // Act & Assert - Both should have identical UTC timestamps
        polygonBarWrapper.TimeUtc.Should().Be(mockAlpacaBar.Object.TimeUtc);
        polygonBarWrapper.TimeUtc.Kind.Should().Be(DateTimeKind.Utc);
        mockAlpacaBar.Object.TimeUtc.Kind.Should().Be(DateTimeKind.Utc);
    }

    private static List<IBar> CreateMockBars(int count)
    {
        var bars = new List<IBar>();
        var basePrice = 100m;
        
        for (int i = 0; i < count; i++)
        {
            var mockBar = new Mock<IBar>();
            var price = basePrice + i;
            
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price * 0.99m);
            mockBar.Setup(x => x.High).Returns(price * 1.01m);
            mockBar.Setup(x => x.Low).Returns(price * 0.98m);
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-count + i));
            
            bars.Add(mockBar.Object);
        }
        
        return bars;
    }

    private static IPosition CreateMockPosition(string symbol, decimal quantity, decimal marketValue)
    {
        var mockPosition = new Mock<IPosition>();
        mockPosition.Setup(x => x.Symbol).Returns(symbol);
        mockPosition.Setup(x => x.Quantity).Returns(quantity);
        mockPosition.Setup(x => x.MarketValue).Returns(marketValue);
        mockPosition.Setup(x => x.UnrealizedProfitLoss).Returns(marketValue * 0.05m); // 5% unrealized gain
        return mockPosition.Object;
    }

    private static IOrder CreateMockOrder(string symbol, OrderSide side, decimal quantity, decimal price)
    {
        var mockOrder = new Mock<IOrder>();
        mockOrder.Setup(x => x.Symbol).Returns(symbol);
        mockOrder.Setup(x => x.OrderSide).Returns(side);
        mockOrder.Setup(x => x.Quantity).Returns(quantity);
        mockOrder.Setup(x => x.AverageFillPrice).Returns(price);
        mockOrder.Setup(x => x.FilledAtUtc).Returns(DateTime.UtcNow.AddMinutes(-30));
        return mockOrder.Object;
    }
}
